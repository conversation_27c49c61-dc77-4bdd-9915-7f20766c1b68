# Transaction Fee Analyzer

This is a fee analysis application that connects to a PostgreSQL database to analyze cryptocurrency transfer fees across different exchanges and networks.

## Features

- Database connection management with YAML configuration
- Transfer fee analysis for the last 90 days
- Support for multiple networks (BTC, ETH, SOL, etc.)
- Exchange-to-exchange transfer analysis
- Comprehensive fee statistics (total, average, transfer counts)

## Setup

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Database**
   - Copy `config.local.yaml` and update with your database credentials
   - The local config file is automatically ignored by git for security

3. **Run the Analyzer**
   ```bash
   # Run the main analyzer
   python -m analyzer

   # Run database tests
   python simple_test.py

   # Test database module directly
   python -m analyzer.db
   ```

## Configuration

The application uses a YAML configuration file with the following structure:

```yaml
database:
  user: your_username
  password: your_password
  database: your_database
  host: your_host
  min_size: 2
  port: 5432
```

## Database Query

The application executes a comprehensive query that analyzes:
- Transfer counts by network and exchange pairs
- Total and average fees in USDT
- Total and average transfer amounts in USDT
- Data from the last 90 days (excluding current day)
- Only successful transfers (status_id = 200)
- Results ordered by total fees (descending)
- Limited to top 100 results

## Output

The analyzer provides:
- Top 10 transfers by total fees
- Summary statistics across all results
- Detailed breakdown by network, exchange, and asset type