# Transaction Fee Analyzer

This is a fee analysis application that connects to a PostgreSQL database to analyze cryptocurrency transfer fees across different exchanges and networks.

## Features

- Database connection management with YAML configuration
- Transfer fee analysis for the last 90 days
- Support for multiple networks (BTC, ETH, SOL, etc.)
- Exchange-to-exchange transfer analysis
- Comprehensive fee statistics (total, average, transfer counts)
- **Advanced Statistical Analysis:**
  - Network-level mean and standard deviation calculations
  - Z-score analysis for exchange-network combinations
  - Logarithmic fee transformation for scoring
  - Potential savings calculations
  - High-impact opportunity identification (scoring > 1 AND savings > $1,000)

## Setup

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Database**
   - Copy `config.local.yaml` and update with your database credentials
   - The local config file is automatically ignored by git for security

3. **Run the Analyzer**
   ```bash
   # Run the main analyzer with advanced analysis
   python -m analyzer

   # Run database tests
   python simple_test.py

   # Test advanced analysis features
   python test_advanced_analysis.py

   # Test database module directly
   python -m analyzer.db
   ```

## Configuration

The application uses a YAML configuration file with the following structure:

```yaml
database:
  user: your_username
  password: your_password
  database: your_database
  host: your_host
  min_size: 2
  port: 5432
```

## Database Query

The application executes a comprehensive query that analyzes:
- Transfer counts by network and exchange pairs
- Total and average fees in USDT
- Total and average transfer amounts in USDT
- Data from the last 90 days (excluding current day)
- Only successful transfers (status_id = 200)
- Results ordered by total fees (descending)
- Limited to top 100 results

## Advanced Analysis Methodology

The application implements sophisticated statistical analysis:

1. **Network Statistics**: Calculates mean and standard deviation of average fees for each network
2. **Z-Score Analysis**: Measures how many standard deviations an exchange's fee is from the network mean
3. **Logarithmic Scoring**: Uses `scoring = z_score × log(total_fee_usdt)` to weight by impact
4. **Potential Savings**: Calculates savings if exchange used network average fee: `(current_fee - network_mean) × transfer_count`
5. **High-Impact Filter**: Identifies opportunities where `scoring > 1 AND potential_savings > $1,000`

## Output

The analyzer provides:
- **Network Summary**: Statistics by network including coefficient of variation
- **High-Impact Opportunities**: Filtered results meeting optimization criteria
- **Detailed Analysis**: Z-scores, scoring, and potential savings for each exchange pair
- **Summary Statistics**: Total potential savings and optimization impact