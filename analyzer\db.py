import psycopg2
import psycopg2.extras
import yaml
import os
from typing import Dict, Any, List, Optional
import pandas as pd
import numpy as np


class DatabaseConnection:
    """Database connection manager for the Transaction Fee Analyzer."""

    def __init__(self, config_path: str = "config.local.yaml"):
        """Initialize database connection with config file."""
        self.config_path = config_path
        self.config = self._load_config()
        self.connection = None

    def _load_config(self) -> Dict[str, Any]:
        """Load database configuration from YAML file."""
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"Config file not found: {self.config_path}")

        with open(self.config_path, 'r') as file:
            config = yaml.safe_load(file)

        if 'database' not in config:
            raise ValueError("Database configuration not found in config file")

        return config['database']

    def connect(self) -> psycopg2.extensions.connection:
        """Establish database connection."""
        try:
            self.connection = psycopg2.connect(
                host=self.config['host'],
                port=self.config['port'],
                database=self.config['database'],
                user=self.config['user'],
                password=self.config['password']
            )
            print(f"Successfully connected to database: {self.config['database']}")
            return self.connection
        except psycopg2.Error as e:
            print(f"Error connecting to database: {e}")
            raise

    def disconnect(self):
        """Close database connection."""
        if self.connection:
            self.connection.close()
            self.connection = None
            print("Database connection closed")

    def execute_query(self, query: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """Execute a query and return results as list of dictionaries."""
        if not self.connection:
            raise RuntimeError("Database connection not established. Call connect() first.")

        try:
            with self.connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                cursor.execute(query, params)
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except psycopg2.Error as e:
            print(f"Error executing query: {e}")
            raise

    def execute_query_to_dataframe(self, query: str, params: Optional[tuple] = None) -> pd.DataFrame:
        """Execute a query and return results as pandas DataFrame."""
        if not self.connection:
            raise RuntimeError("Database connection not established. Call connect() first.")

        try:
            return pd.read_sql_query(query, self.connection, params=params)
        except Exception as e:
            print(f"Error executing query to DataFrame: {e}")
            raise


def get_transfer_fee_analysis() -> pd.DataFrame:
    """
    Get transfer fee analysis for the last 90 days.

    Returns:
        pd.DataFrame: Analysis results with columns:
            - network: Network name
            - exchange_from: Source exchange
            - gt_asset: Asset type
            - exchange_to: Destination exchange
            - transfer_count: Number of transfers
            - total_fee_usdt: Total fees in USDT
            - avg_fee_usdt: Average fee in USDT
            - total_transfer_usdt: Total transfer amount in USDT
            - avg_transfer_usdt: Average transfer amount in USDT
    """
    query = """
    SELECT
        t.network,
        sm_from.exchange AS exchange_from,
        t.gt_asset,
        sm_to.exchange   AS exchange_to,
        COUNT(*)                              AS transfer_count,
        SUM(t.gt_fee_in_usdt)                 AS total_fee_usdt,
        AVG(t.gt_fee_in_usdt)                 AS avg_fee_usdt,
        SUM(t.gt_amount_in_usdt)              AS total_transfer_usdt,
        AVG(t.gt_amount_in_usdt)              AS avg_transfer_usdt
    FROM capman.transfers        AS t
    JOIN capman.sources_map      AS sm_from ON t.gt_source_from = sm_from.gt_source
    JOIN capman.sources_map      AS sm_to   ON t.gt_source_to   = sm_to.gt_source
    WHERE t.gt_timestamp >= (CURRENT_DATE - INTERVAL '90 days')   -- 90 days ago
        AND t.gt_timestamp <  CURRENT_DATE -- Exclude current day to have the same data as in BI
    AND  t.gt_status_id   IN (200)
    GROUP BY
        t.network,
        t.gt_asset,
        sm_from.exchange,
        sm_to.exchange
    ORDER BY total_fee_usdt desc
    LIMIT 100;
    """

    db = DatabaseConnection()
    try:
        db.connect()
        return db.execute_query_to_dataframe(query)
    finally:
        db.disconnect()


def calculate_advanced_fee_analysis(df: pd.DataFrame) -> pd.DataFrame:
    """
    Calculate advanced fee analysis including z-scores, log fees, scoring, and potential savings.

    Args:
        df: DataFrame from get_transfer_fee_analysis()

    Returns:
        pd.DataFrame: Enhanced analysis with additional columns:
            - network_mean_fee: Mean average fee for the network across all exchanges
            - network_stdev_fee: Standard deviation of fees for the network
            - z_score: Z-score for the exchange and network combination
            - log_total_fee: Natural logarithm of total fees
            - scoring: Z-score multiplied by log of total fees
            - potential_savings: Savings if fee was network average
            - meets_criteria: Boolean filter (scoring > 1 AND savings > 1000)
    """
    # Create a copy to avoid modifying the original DataFrame
    analysis_df = df.copy()

    # Calculate network-level statistics (mean and standard deviation of avg_fee_usdt by network)
    network_stats = analysis_df.groupby('network')['avg_fee_usdt'].agg(['mean', 'std']).reset_index()
    network_stats.columns = ['network', 'network_mean_fee', 'network_stdev_fee']

    # Handle cases where standard deviation is 0 or NaN (only one exchange for a network)
    network_stats['network_stdev_fee'] = network_stats['network_stdev_fee'].fillna(1.0)
    network_stats.loc[network_stats['network_stdev_fee'] == 0, 'network_stdev_fee'] = 1.0

    # Merge network statistics back to the main DataFrame
    analysis_df = analysis_df.merge(network_stats, on='network', how='left')

    # Calculate Z-score for each exchange-network combination
    analysis_df['z_score'] = (
        (analysis_df['avg_fee_usdt'] - analysis_df['network_mean_fee']) /
        analysis_df['network_stdev_fee']
    )

    # Calculate log of total fees (handle zero or negative values)
    analysis_df['log_total_fee'] = np.log(np.maximum(analysis_df['total_fee_usdt'], 0.01))

    # Calculate scoring as z_score * log_total_fee
    analysis_df['scoring'] = analysis_df['z_score'] * analysis_df['log_total_fee']

    # Calculate potential savings if fee was network average
    # Savings = (current_avg_fee - network_avg_fee) * transfer_count
    analysis_df['potential_savings'] = (
        (analysis_df['avg_fee_usdt'] - analysis_df['network_mean_fee']) *
        analysis_df['transfer_count']
    )

    # Apply filter criteria: scoring > 1 AND potential_savings > 1000
    analysis_df['meets_criteria'] = (
        (analysis_df['scoring'] > 1) &
        (analysis_df['potential_savings'] > 1000)
    )

    return analysis_df


def get_filtered_high_impact_transfers() -> pd.DataFrame:
    """
    Get transfer fee analysis with advanced analytics and apply filtering criteria.

    Returns:
        pd.DataFrame: Filtered results where scoring > 1 and potential savings > 1000
    """
    # Get base data
    base_df = get_transfer_fee_analysis()

    # Apply advanced analysis
    enhanced_df = calculate_advanced_fee_analysis(base_df)

    # Filter based on criteria
    filtered_df = enhanced_df[enhanced_df['meets_criteria']].copy()

    # Sort by potential savings (descending)
    filtered_df = filtered_df.sort_values('potential_savings', ascending=False)

    return filtered_df


def get_network_summary_statistics() -> pd.DataFrame:
    """
    Get summary statistics by network for fee analysis.

    Returns:
        pd.DataFrame: Network-level summary with statistics
    """
    # Get base data
    base_df = get_transfer_fee_analysis()

    # Calculate network-level aggregations
    network_summary = base_df.groupby('network').agg({
        'transfer_count': 'sum',
        'total_fee_usdt': 'sum',
        'avg_fee_usdt': ['mean', 'std', 'min', 'max'],
        'exchange_from': 'nunique'  # Number of unique exchanges
    }).round(4)

    # Flatten column names
    network_summary.columns = [
        'total_transfers', 'total_fees', 'mean_avg_fee', 'stdev_avg_fee',
        'min_avg_fee', 'max_avg_fee', 'unique_exchanges'
    ]

    # Reset index to make network a column
    network_summary = network_summary.reset_index()

    # Calculate coefficient of variation (relative variability)
    network_summary['cv_fee'] = (
        network_summary['stdev_avg_fee'] / network_summary['mean_avg_fee']
    ).round(4)

    # Sort by total fees
    network_summary = network_summary.sort_values('total_fees', ascending=False)

    return network_summary


if __name__ == "__main__":
    # Test the database connection and advanced analysis
    try:
        print("Testing database connection and advanced analysis...")

        # Test basic query
        print("\n1. Basic Transfer Fee Analysis:")
        df = get_transfer_fee_analysis()
        print(f"✓ Query executed successfully! Retrieved {len(df)} rows.")

        # Test advanced analysis
        print("\n2. Advanced Fee Analysis:")
        enhanced_df = calculate_advanced_fee_analysis(df)
        print(f"✓ Advanced analysis completed. Added {len(enhanced_df.columns) - len(df.columns)} new columns.")
        print(f"New columns: {list(set(enhanced_df.columns) - set(df.columns))}")

        # Test filtered results
        print("\n3. High-Impact Transfers (scoring > 1 AND savings > 1000):")
        filtered_df = get_filtered_high_impact_transfers()
        print(f"✓ Found {len(filtered_df)} high-impact transfers")

        if len(filtered_df) > 0:
            print("\nTop 3 high-impact transfers:")
            for i, row in filtered_df.head(3).iterrows():
                print(f"  {row['network']} | {row['exchange_from']} → {row['exchange_to']} | "
                      f"Scoring: {row['scoring']:.2f} | Savings: ${row['potential_savings']:.2f}")

        # Test network summary
        print("\n4. Network Summary Statistics:")
        network_summary = get_network_summary_statistics()
        print(f"✓ Network summary completed for {len(network_summary)} networks")
        print("\nTop 3 networks by total fees:")
        for i, row in network_summary.head(3).iterrows():
            print(f"  {row['network']}: ${row['total_fees']:.2f} total, "
                  f"{row['unique_exchanges']} exchanges, CV: {row['cv_fee']:.3f}")

    except Exception as e:
        print(f"Error: {e}")