import psycopg2
import psycopg2.extras
import yaml
import os
from typing import Dict, Any, List, Optional
import pandas as pd


class DatabaseConnection:
    """Database connection manager for the Transaction Fee Analyzer."""

    def __init__(self, config_path: str = "config.local.yaml"):
        """Initialize database connection with config file."""
        self.config_path = config_path
        self.config = self._load_config()
        self.connection = None

    def _load_config(self) -> Dict[str, Any]:
        """Load database configuration from YAML file."""
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"Config file not found: {self.config_path}")

        with open(self.config_path, 'r') as file:
            config = yaml.safe_load(file)

        if 'database' not in config:
            raise ValueError("Database configuration not found in config file")

        return config['database']

    def connect(self) -> psycopg2.extensions.connection:
        """Establish database connection."""
        try:
            self.connection = psycopg2.connect(
                host=self.config['host'],
                port=self.config['port'],
                database=self.config['database'],
                user=self.config['user'],
                password=self.config['password']
            )
            print(f"Successfully connected to database: {self.config['database']}")
            return self.connection
        except psycopg2.Error as e:
            print(f"Error connecting to database: {e}")
            raise

    def disconnect(self):
        """Close database connection."""
        if self.connection:
            self.connection.close()
            self.connection = None
            print("Database connection closed")

    def execute_query(self, query: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """Execute a query and return results as list of dictionaries."""
        if not self.connection:
            raise RuntimeError("Database connection not established. Call connect() first.")

        try:
            with self.connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                cursor.execute(query, params)
                results = cursor.fetchall()
                return [dict(row) for row in results]
        except psycopg2.Error as e:
            print(f"Error executing query: {e}")
            raise

    def execute_query_to_dataframe(self, query: str, params: Optional[tuple] = None) -> pd.DataFrame:
        """Execute a query and return results as pandas DataFrame."""
        if not self.connection:
            raise RuntimeError("Database connection not established. Call connect() first.")

        try:
            return pd.read_sql_query(query, self.connection, params=params)
        except Exception as e:
            print(f"Error executing query to DataFrame: {e}")
            raise


def get_transfer_fee_analysis(days: int = 90) -> pd.DataFrame:
    """
    Get transfer fee analysis for the specified number of days.

    Args:
        days: Number of days to analyze (default: 90)

    Returns:
        pd.DataFrame: Analysis results with columns:
            - network: Network name
            - exchange_from: Source exchange
            - gt_asset: Asset type
            - exchange_to: Destination exchange
            - transfer_count: Number of transfers
            - total_fee_usdt: Total fees in USDT
            - avg_fee_usdt: Average fee in USDT
            - total_transfer_usdt: Total transfer amount in USDT
            - avg_transfer_usdt: Average transfer amount in USDT
    """
    query = f"""
    SELECT
        t.network,
        sm_from.exchange AS exchange_from,
        t.gt_asset,
        -- sm_to.exchange   AS exchange_to,
        COUNT(*)                              AS transfer_count,
        SUM(t.gt_fee_in_usdt)                 AS total_fee_usdt,
        SUM(t.gt_amount_in_usdt)              AS total_transfer_usdt
    FROM capman.transfers        AS t
    JOIN capman.sources_map      AS sm_from ON t.gt_source_from = sm_from.gt_source
    JOIN capman.sources_map      AS sm_to   ON t.gt_source_to   = sm_to.gt_source
    WHERE t.gt_timestamp >= (CURRENT_DATE - INTERVAL '{days}' days)
        AND t.gt_timestamp <  CURRENT_DATE -- Exclude current day to have the same data as in BI
    AND  t.gt_status_id   IN (200)
    GROUP BY
        t.network,
        t.gt_asset,
        sm_from.exchange,
        sm_to.exchange
    ORDER BY total_fee_usdt desc;
    """

    db = DatabaseConnection()
    try:
        db.connect()
        return db.execute_query_to_dataframe(query)
    finally:
        db.disconnect()


if __name__ == "__main__":
    # Test the database connection and query
    try:
        print("Testing database connection and query...")
        df = get_transfer_fee_analysis()
        print(f"Query executed successfully! Retrieved {len(df)} rows.")
        print("\nFirst 5 rows:")
        print(df.head())
        print("\nColumn info:")
        print(df.info())
    except Exception as e:
        print(f"Error: {e}")