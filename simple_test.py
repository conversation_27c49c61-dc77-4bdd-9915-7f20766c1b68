#!/usr/bin/env python3
"""
Simple test to verify database connection and basic query functionality.
"""

from analyzer.db import DatabaseConnection, get_transfer_fee_analysis


def test_basic_connection():
    """Test basic database connection."""
    print("Testing basic database connection...")
    
    db = DatabaseConnection()
    try:
        db.connect()
        print("✓ Connection successful!")
        
        # Test simple query
        result = db.execute_query("SELECT 1 as test")
        print(f"✓ Simple query result: {result}")
        
        return True
    except Exception as e:
        print(f"✗ Connection failed: {e}")
        return False
    finally:
        db.disconnect()


def test_transfer_analysis():
    """Test the transfer fee analysis query."""
    print("\nTesting transfer fee analysis query...")
    
    try:
        df = get_transfer_fee_analysis()
        print(f"✓ Query successful! Retrieved {len(df)} rows")
        
        if len(df) > 0:
            print(f"✓ Columns: {list(df.columns)}")
            print(f"✓ Top transfer by total fee:")
            top_row = df.iloc[0]
            print(f"   Network: {top_row['network']}")
            print(f"   From: {top_row['exchange_from']} → To: {top_row['exchange_to']}")
            print(f"   Asset: {top_row['gt_asset']}")
            print(f"   Total Fee: ${top_row['total_fee_usdt']:,.2f}")
            print(f"   Transfer Count: {top_row['transfer_count']}")
        
        return True
    except Exception as e:
        print(f"✗ Query failed: {e}")
        return False


if __name__ == "__main__":
    print("=== Transaction Fee Analyzer - Database Tests ===")
    
    # Test 1: Basic connection
    if test_basic_connection():
        # Test 2: Transfer analysis
        if test_transfer_analysis():
            print("\n🎉 All tests passed! Database is working correctly.")
        else:
            print("\n❌ Transfer analysis test failed.")
    else:
        print("\n❌ Basic connection test failed.")
