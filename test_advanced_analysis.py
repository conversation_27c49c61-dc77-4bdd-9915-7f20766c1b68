#!/usr/bin/env python3
"""
Test script for advanced fee analysis features.
"""

from analyzer.db import (
    get_transfer_fee_analysis,
    calculate_advanced_fee_analysis,
    get_filtered_high_impact_transfers,
    get_network_summary_statistics
)
import pandas as pd


def test_advanced_analysis():
    """Test the advanced analysis calculations."""
    print("=== Testing Advanced Fee Analysis ===")
    
    try:
        # Get base data
        print("1. Fetching base data...")
        df = get_transfer_fee_analysis()
        print(f"✓ Retrieved {len(df)} records")
        
        # Test advanced analysis
        print("\n2. Testing advanced analysis calculations...")
        enhanced_df = calculate_advanced_fee_analysis(df)
        
        # Verify new columns exist
        expected_columns = [
            'network_mean_fee', 'network_stdev_fee', 'z_score', 
            'log_total_fee', 'scoring', 'potential_savings', 'meets_criteria'
        ]
        
        missing_columns = [col for col in expected_columns if col not in enhanced_df.columns]
        if missing_columns:
            print(f"✗ Missing columns: {missing_columns}")
            return False
        
        print(f"✓ All expected columns present: {expected_columns}")
        
        # Check data types and ranges
        print("\n3. Validating calculations...")
        
        # Check for NaN values in critical columns
        critical_cols = ['z_score', 'scoring', 'potential_savings']
        for col in critical_cols:
            nan_count = enhanced_df[col].isna().sum()
            if nan_count > 0:
                print(f"⚠ Warning: {nan_count} NaN values in {col}")
            else:
                print(f"✓ No NaN values in {col}")
        
        # Check log values are reasonable
        if (enhanced_df['log_total_fee'] < 0).any():
            print("⚠ Warning: Some log_total_fee values are negative")
        else:
            print("✓ All log_total_fee values are non-negative")
        
        # Show sample calculations
        print(f"\n4. Sample calculations (first 3 rows):")
        sample_cols = ['network', 'exchange_from', 'avg_fee_usdt', 'network_mean_fee', 
                      'z_score', 'scoring', 'potential_savings', 'meets_criteria']
        print(enhanced_df[sample_cols].head(3).to_string(index=False))
        
        return True
        
    except Exception as e:
        print(f"✗ Advanced analysis test failed: {e}")
        return False


def test_filtered_results():
    """Test the filtered high-impact transfers."""
    print("\n=== Testing Filtered High-Impact Transfers ===")
    
    try:
        filtered_df = get_filtered_high_impact_transfers()
        print(f"✓ Retrieved {len(filtered_df)} high-impact transfers")
        
        if len(filtered_df) > 0:
            # Verify filtering criteria
            scoring_check = (filtered_df['scoring'] > 1).all()
            savings_check = (filtered_df['potential_savings'] > 1000).all()
            
            print(f"✓ All records have scoring > 1: {scoring_check}")
            print(f"✓ All records have savings > $1,000: {savings_check}")
            
            # Show top results
            print(f"\nTop 3 high-impact opportunities:")
            for i, row in filtered_df.head(3).iterrows():
                print(f"  {row['network']} | {row['exchange_from']} → {row['exchange_to']}")
                print(f"    Scoring: {row['scoring']:.2f}, Z-Score: {row['z_score']:.2f}")
                print(f"    Potential Savings: ${row['potential_savings']:.2f}")
                print()
        else:
            print("ℹ No transfers meet the filtering criteria")
        
        return True
        
    except Exception as e:
        print(f"✗ Filtered results test failed: {e}")
        return False


def test_network_summary():
    """Test the network summary statistics."""
    print("\n=== Testing Network Summary Statistics ===")
    
    try:
        summary_df = get_network_summary_statistics()
        print(f"✓ Generated summary for {len(summary_df)} networks")
        
        # Check expected columns
        expected_cols = [
            'network', 'total_transfers', 'total_fees', 'mean_avg_fee',
            'stdev_avg_fee', 'min_avg_fee', 'max_avg_fee', 'unique_exchanges', 'cv_fee'
        ]
        
        missing_cols = [col for col in expected_cols if col not in summary_df.columns]
        if missing_cols:
            print(f"✗ Missing columns: {missing_cols}")
            return False
        
        print(f"✓ All expected columns present")
        
        # Show top networks
        print(f"\nTop 3 networks by total fees:")
        for i, row in summary_df.head(3).iterrows():
            print(f"  {row['network']}: ${row['total_fees']:,.2f} total fees")
            print(f"    {row['unique_exchanges']} exchanges, CV: {row['cv_fee']:.3f}")
            print(f"    Fee range: ${row['min_avg_fee']:.2f} - ${row['max_avg_fee']:.2f}")
            print()
        
        return True
        
    except Exception as e:
        print(f"✗ Network summary test failed: {e}")
        return False


def main():
    """Run all advanced analysis tests."""
    print("Advanced Transaction Fee Analysis - Test Suite")
    print("=" * 60)
    
    tests = [
        test_advanced_analysis,
        test_filtered_results,
        test_network_summary
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        if test_func():
            passed += 1
    
    print(f"\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Advanced analysis is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit(main())
