#!/usr/bin/env python3
"""
Main entry point for the Transaction Fee Analyzer.
"""

from .db import get_transfer_fee_analysis


def main():
    """Main function to run the transfer fee analysis."""
    print("Transaction Fee Analyzer")
    print("=" * 40)

    try:
        print("Fetching transfer fee analysis data...")
        df = get_transfer_fee_analysis()

        print(f"✓ Successfully retrieved {len(df)} records")
        print("\nTop 10 transfers by total fees:")
        print("-" * 80)

        # Display top 10 results in a formatted way
        for i, row in df.head(10).iterrows():
            print(f"{i+1:2d}. {row['network']:>3} | {row['exchange_from']:>15} → {row['exchange_to']:<15} | "
                  f"{row['gt_asset']:>4} | ${row['total_fee_usdt']:>10,.2f} | "
                  f"{row['transfer_count']:>3} transfers")

        print(f"\nTotal fees across all top 100 transfers: ${df['total_fee_usdt'].sum():,.2f}")
        print(f"Average fee per transfer: ${df['avg_fee_usdt'].mean():.2f}")

    except Exception as e:
        print(f"Error: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())