#!/usr/bin/env python3
"""
Main entry point for the Transaction Fee Analyzer.
"""

from .db import (
    get_transfer_fee_analysis,
    get_filtered_high_impact_transfers,
    get_network_summary_statistics,
    calculate_advanced_fee_analysis
)


def main():
    """Main function to run the advanced transfer fee analysis."""
    print("Advanced Transaction Fee Analyzer")
    print("=" * 50)

    try:
        # 1. Basic Analysis
        print("1. Fetching base transfer fee data...")
        df = get_transfer_fee_analysis()
        print(f"✓ Successfully retrieved {len(df)} records")

        # 2. Advanced Analysis
        print("\n2. Performing advanced statistical analysis...")
        enhanced_df = calculate_advanced_fee_analysis(df)
        print(f"✓ Enhanced analysis completed with {len(enhanced_df.columns)} total columns")

        # 3. Network Summary
        print("\n3. Network Summary Statistics:")
        print("-" * 70)
        network_summary = get_network_summary_statistics()

        for i, row in network_summary.head(5).iterrows():
            print(f"{row['network']:>3} | ${row['total_fees']:>10,.2f} | "
                  f"{row['unique_exchanges']:>2} exchanges | "
                  f"Mean: ${row['mean_avg_fee']:>6.2f} | "
                  f"CV: {row['cv_fee']:>5.3f}")

        # 4. High-Impact Transfers (Filtered Results)
        print("\n4. High-Impact Transfer Opportunities:")
        print("   (Scoring > 1 AND Potential Savings > $1,000)")
        print("-" * 90)

        filtered_df = get_filtered_high_impact_transfers()

        if len(filtered_df) > 0:
            print(f"✓ Found {len(filtered_df)} high-impact opportunities")
            print("\nTop 10 optimization opportunities:")

            for i, row in filtered_df.head(10).iterrows():
                print(f"{i+1:2d}. {row['network']:>3} | {row['exchange_from']:>15} → {row['exchange_to']:<15} | "
                      f"Score: {row['scoring']:>6.2f} | "
                      f"Z-Score: {row['z_score']:>5.2f} | "
                      f"Savings: ${row['potential_savings']:>8,.2f}")

            # Summary statistics for filtered results
            total_potential_savings = filtered_df['potential_savings'].sum()
            avg_scoring = filtered_df['scoring'].mean()

            print(f"\n📊 Summary of High-Impact Opportunities:")
            print(f"   Total Potential Savings: ${total_potential_savings:,.2f}")
            print(f"   Average Scoring: {avg_scoring:.2f}")
            print(f"   Networks involved: {filtered_df['network'].nunique()}")
            print(f"   Exchange pairs: {len(filtered_df)}")

        else:
            print("⚠ No transfers meet the high-impact criteria (scoring > 1 AND savings > $1,000)")

        # 5. Overall Summary
        print(f"\n5. Overall Analysis Summary:")
        print("-" * 50)
        print(f"Total transfers analyzed: {df['transfer_count'].sum():,}")
        print(f"Total fees: ${df['total_fee_usdt'].sum():,.2f}")
        print(f"Networks: {df['network'].nunique()}")
        print(f"Exchange pairs: {len(df)}")
        if len(filtered_df) > 0:
            savings_percentage = (filtered_df['potential_savings'].sum() / df['total_fee_usdt'].sum()) * 100
            print(f"Potential savings: {savings_percentage:.1f}% of total fees")

    except Exception as e:
        print(f"Error: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())